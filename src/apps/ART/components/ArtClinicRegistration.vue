<template>
    <ion-card style="margin: 0px auto; width:80%;">
        <ion-card-content>
            <ion-grid>
                <!-- Phone and Home Visit Followup -->
                <ion-row>
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">Phone Followup <span style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.phone_followup"
                            @ionChange="(e) => { formData.phone_followup = e.detail.value; dataHandler('phone_followup', e.detail.value); }">
                            <ion-item lines="none">
                                <ion-radio slot="start" value="Yes"></ion-radio>
                                <ion-label>Yes</ion-label>
                            </ion-item>
                            <ion-item lines="none">
                                <ion-radio slot="start" value="No"></ion-radio>
                                <ion-label>No</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.phone_followup" color="danger">
                            {{ validationMessages.phone_followup }}
                        </ion-note>
                    </ion-col>
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">Home Visit Followup <span style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.home_visit_followup"
                            @ionChange="(e) => { formData.home_visit_followup = e.detail.value; dataHandler('home_visit_followup', e.detail.value); }">
                            <ion-item lines="none">
                                <ion-radio slot="start" value="Yes"></ion-radio>
                                <ion-label>Yes</ion-label>
                            </ion-item>
                            <ion-item lines="none">
                                <ion-radio slot="start" value="No"></ion-radio>
                                <ion-label>No</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.home_visit_followup" color="danger">
                            {{ validationMessages.home_visit_followup }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Has Linkage -->
                <ion-row v-if="formModel.has_linkage.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.has_linkage.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.has_linkage"
                            @ionChange="(e) => { formData.has_linkage = e.detail.value; dataHandler('has_linkage', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.has_linkage.options()" :key="option">
                                <ion-radio slot="start" :value="option"></ion-radio>
                                <ion-label>{{ option }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.has_linkage" color="danger">
                            {{ validationMessages.has_linkage }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <ion-row v-if="formModel.linkage_number.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.linkage_number.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="e.g 1234-002-01-A"
                            :inputValue="formData.linkage_number"
                            @update:inputValue="(e) => { formData.linkage_number = e.target.value; dataHandler('linkage_number', e.target.value); }"
                            :error="!!validationMessages.linkage_number">
                            <template #end>
                                <ion-label>set_site_prefix</ion-label>
                            </template>
                        </BasicInputField>
                        <ion-note v-if="validationMessages.linkage_number" color="danger">
                            {{ validationMessages.linkage_number }}
                        </ion-note>
                    </ion-col>
                </ion-row>
                <!-- Received ARVs -->
                <ion-row v-if="formModel.received_arvs.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.received_arvs.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.received_arvs"
                            @ionChange="(e) => { formData.received_arvs = e.detail.value; dataHandler('received_arvs', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.received_arvs.options()" :key="option">
                                <ion-radio slot="start" :value="option"></ion-radio>
                                <ion-label>{{ option }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.received_arvs" color="danger">
                            {{ validationMessages.received_arvs }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Date last taken ARVs -->
                <ion-row v-if="formModel.date_last_taken_arvs.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">
                            {{ formModel.date_last_taken_arvs.label }}
                            <span style="color: red">*</span>
                        </ion-label>
                        <DatePicker place_holder="" :date_prop="formData.date_last_taken_arvs"
                            @date-up-dated="(value: any) => { formData.date_last_taken_arvs = value.value.standardDate; dataHandler('date_last_taken_arvs', value.value.standardDate); }"
                            :error="!!validationMessages.date_last_taken_arvs" />
                        <ion-note v-if="validationMessages.date_last_taken_arvs" color="danger">
                            {{ validationMessages.date_last_taken_arvs }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Taken last two months -->
                <ion-row v-if="formModel.taken_last_two_months.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.taken_last_two_months.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.taken_last_two_months"
                            @ionChange="(e) => { formData.taken_last_two_months = e.detail.value; dataHandler('taken_last_two_months', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.taken_last_two_months.options()"
                                :key="option">
                                <ion-radio slot="start" :value="option"></ion-radio>
                                <ion-label>{{ option }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.taken_last_two_months" color="danger">
                            {{ validationMessages.taken_last_two_months }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Taken last two weeks -->
                <ion-row v-if="formModel.taken_last_two_weeks.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.taken_last_two_weeks.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.taken_last_two_weeks"
                            @ionChange="(e) => { formData.taken_last_two_weeks = e.detail.value; dataHandler('taken_last_two_weeks', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.taken_last_two_weeks.options()"
                                :key="option">
                                <ion-radio slot="start" :value="option"></ion-radio>
                                <ion-label>{{ option }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.taken_last_two_weeks" color="danger">
                            {{ validationMessages.taken_last_two_weeks }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Ever registered at ART clinic -->
                <ion-row v-if="formModel.ever_registered_at_art_clinic.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.ever_registered_at_art_clinic.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.ever_registered_at_art_clinic"
                            @ionChange="(e) => { formData.ever_registered_at_art_clinic = e.detail.value; dataHandler('ever_registered_at_art_clinic', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.ever_registered_at_art_clinic.options()"
                                :key="option">
                                <ion-radio slot="start" :value="option"></ion-radio>
                                <ion-label>{{ option }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.ever_registered_at_art_clinic" color="danger">
                            {{ validationMessages.ever_registered_at_art_clinic }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Location of ART Initiation -->
                <ion-row v-if="formModel.location_of_art_initialization.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.location_of_art_initialization.label }} <span
                                style="color: red">*</span></ion-label>
                        <SelectFacility :show_error="!!validationMessages.location_of_art_initialization"
                            :selected_district_ids="[]" :selected_location="formData.location_of_art_initialization"
                            @facilitySelected="(value: any) => { formData.location_of_art_initialization = value.selected_location; dataHandler('location_of_art_initialization', value.selected_location); }" />
                        <ion-note v-if="validationMessages.location_of_art_initialization" color="danger">
                            {{ validationMessages.location_of_art_initialization }}
                        </ion-note>
                    </ion-col>
                    <ion-col v-if="formModel.art_number_at_previous_location.required()" size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.art_number_at_previous_location.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="Enter previous ART number"
                            :inputValue="formData.art_number_at_previous_location"
                            @update:inputValue="(e) => { formData.art_number_at_previous_location = e.target.value; dataHandler('art_number_at_previous_location', e.target.value); }"
                            :error="!!validationMessages.art_number_at_previous_location" />
                        <ion-note v-if="validationMessages.art_number_at_previous_location" color="danger">
                            {{ validationMessages.art_number_at_previous_location }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- ART Start Date -->
                <ion-row v-if="formModel.art_start_date.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.art_start_date.label }} <span
                                style="color: red">*</span></ion-label>
                        <DatePicker
                            :place_holder="formModel.art_start_date.label + (formModel.art_start_date.required() ? '*' : '')"
                            :date_prop="formData.art_start_date"
                            @date-up-dated="(value: any) => { formData.art_start_date = value.value.standardDate; dataHandler('art_start_date', value.value.standardDate); }"
                            :error="!!validationMessages.art_start_date" />
                        <ion-note v-if="validationMessages.art_start_date" color="danger">
                            {{ validationMessages.art_start_date }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Has Transfer Letter -->
                <ion-row v-if="formModel.has_transfer_letter.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.has_transfer_letter.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.has_transfer_letter"
                            @ionChange="(e) => { formData.has_transfer_letter = e.detail.value; dataHandler('has_transfer_letter', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.has_transfer_letter.options()"
                                :key="option">
                                <ion-radio slot="start" :value="option"></ion-radio>
                                <ion-label>{{ option }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.has_transfer_letter" color="danger">
                            {{ validationMessages.has_transfer_letter }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Initial Weight and Height -->
                <ion-row>
                    <ion-col size="12" size-md="6" v-if="formModel.initial_weight.required()">
                        <ion-label class="form-label">{{ formModel.initial_weight.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="1" inputType="number"
                            :inputValue="formData.initial_weight"
                            @update:inputValue="(e) => { formData.initial_weight = e.target.value; dataHandler('initial_weight', e.target.value); }"
                            :error="!!validationMessages.initial_weight" unit="KG" />
                        <ion-note v-if="validationMessages.initial_weight" color="danger">
                            {{ validationMessages.initial_weight }}
                        </ion-note>
                    </ion-col>
                    <ion-col size="12" size-md="6" v-if="formModel.initial_height.required()">
                        <ion-label class="form-label">{{ formModel.initial_height.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="1" inputType="number"
                            :inputValue="formData.initial_height"
                            @update:inputValue="(e) => { formData.initial_height = e.target.value; dataHandler('initial_height', e.target.value); }"
                            :error="!!validationMessages.initial_height" unit="CM" />
                        <ion-note v-if="validationMessages.initial_height" color="danger">
                            {{ validationMessages.initial_height }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <ion-row v-if="formData.has_transfer_letter === 'Yes'">
                    <ion-col>
                        <Staging :key="formData.art_start_date" :date="formData.art_start_date" ref="stagingRef" />
                    </ion-col>
                </ion-row>

                <!-- CD4 Available -->
                <ion-row v-if="formModel.cd4_available.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.cd4_available.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.cd4_available"
                            @ionChange="(e) => { formData.cd4_available = e.detail.value; dataHandler('cd4_available', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.cd4_available.options()" :key="option">
                                <ion-radio slot="start" :value="option"></ion-radio>
                                <ion-label>{{ option }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.cd4_available" color="danger">
                            {{ validationMessages.cd4_available }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- CD4 Percent -->
                <ion-row v-if="formModel.cd4_percent.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.cd4_percent.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="Enter CD4 percent" inputType="number"
                            :inputValue="formData.cd4_percent"
                            @update:inputValue="(e) => { formData.cd4_percent = e.target.value; dataHandler('cd4_percent', e.target.value); }"
                            :error="!!validationMessages.cd4_percent" unit="%" />
                        <ion-note v-if="validationMessages.cd4_percent" color="danger">
                            {{ validationMessages.cd4_percent }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Confirmatory HIV Test Type -->
                <ion-row v-if="formModel.confirmatory_hiv_test_type.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.confirmatory_hiv_test_type.label }} <span
                                style="color: red">*</span></ion-label>
                        <ion-radio-group :value="formData.confirmatory_hiv_test_type"
                            @ionChange="(e) => { formData.confirmatory_hiv_test_type = e.detail.value; dataHandler('confirmatory_hiv_test_type', e.detail.value); }">
                            <ion-item lines="none" v-for="option in formModel.confirmatory_hiv_test_type.options()"
                                :key="option.value">
                                <ion-radio :disabled="option.disabled ? option.disabled() : false" slot="start"
                                    :value="option.value"></ion-radio>
                                <ion-label>{{ option.label }}</ion-label>
                            </ion-item>
                        </ion-radio-group>
                        <ion-note v-if="validationMessages.confirmatory_hiv_test_type" color="danger">
                            {{ validationMessages.confirmatory_hiv_test_type }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Confirmatory HIV Test Location -->
                <ion-row v-if="formModel.confirmatory_hiv_test_location.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.confirmatory_hiv_test_location.label }} <span
                                style="color: red">*</span></ion-label>
                        <SelectFacility :show_error="!!validationMessages.confirmatory_hiv_test_location"
                            :selected_district_ids="[]" :selected_location="formData.confirmatory_hiv_test_location"
                            @facilitySelected="(value: any) => { formData.confirmatory_hiv_test_location = value.selected_location; dataHandler('confirmatory_hiv_test_location', value.selected_location); }" />
                        <ion-note v-if="validationMessages.confirmatory_hiv_test_location" color="danger">
                            {{ validationMessages.confirmatory_hiv_test_location }}
                        </ion-note>
                    </ion-col>
                    <ion-col v-if="formModel.confirmatory_hiv_test_date.required()" size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.confirmatory_hiv_test_date.label }} <span
                                style="color: red">*</span></ion-label>
                        <DatePicker
                            :place_holder="formModel.confirmatory_hiv_test_date.label + (formModel.confirmatory_hiv_test_date.required() ? '*' : '')"
                            :date_prop="formData.confirmatory_hiv_test_date"
                            @date-up-dated="(value: any) => { formData.confirmatory_hiv_test_date = value.value.standardDate; dataHandler('confirmatory_hiv_test_date', value.value.standardDate); }"
                            :error="!!validationMessages.confirmatory_hiv_test_date" />
                        <ion-note v-if="validationMessages.confirmatory_hiv_test_date" color="danger">
                            {{ validationMessages.confirmatory_hiv_test_date }}
                        </ion-note>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ARTClinicRegistrationService } from "../services/art_clinic_registration_service";
import { PatientService } from "@/services/patient_service";
import { validateScanFormLinkageCode } from "@/utils/Damm"
import { getFacilities } from "@/utils/HisFormHelpers/LocationFieldOptions";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";
import {
    IonCard,
    IonCardContent,
    IonGrid,
    IonRow,
    IonCol,
    IonLabel,
    IonRadioGroup,
    IonRadio,
    IonItem,
    IonNote
} from "@ionic/vue";
import Staging from "./Staging.vue";

const patient = new PatientService()
const service = new ARTClinicRegistrationService(patient.getID(), -1);
const stagingRef = ref()

const formData = ref<any>({
    phone_followup: '' as string,
    home_visit_followup: '' as string,
    has_linkage: '' as string,
    linkage_number: '' as string,
    received_arvs: '' as string,
    date_last_taken_arvs: '' as string,
    taken_last_two_months: '' as string,
    taken_last_two_weeks: '' as string,
    ever_registered_at_art_clinic: '' as string,
    location_of_art_initialization: '' as string,
    art_number_at_previous_location: '' as string,
    confirmatory_hiv_test_type: '' as string,
    confirmatory_hiv_test_location: '' as string,
    confirmatory_hiv_test_date: '' as string,
    has_transfer_letter: '' as string,
    art_start_date: '' as string,
    initial_weight: '' as string,
    initial_height: '' as string,
    cd4_available: '' as string,
    cd4_percent: '' as string
})

const facilities = ref<any>([])
const validationMessages = ref<any>({})

const formModel: any = {
    phone_followup: {
        required: () => true,
        label: 'Phone followup',
        buildObs: () => {
            return [
                service.buildValueCoded("Phone", formData.value.phone_followup),
                service.buildValueCoded('Agrees to followup', "Phone")
            ]
        },
        options: () => ['Yes', 'No'],
        validation: () => {
            validationMessages.value['phone_followup'] = ''
            if (!formData.value.phone_followup) {
                validationMessages.value['phone_followup'] = 'Phone followup is required'
            }
        }
    },
    home_visit_followup: {
        required: () => true,
        label: 'Home visit followup',
        options: () => ['Yes', 'No'],
        buildObs: () => {
            return [
                service.buildValueCoded("Home visit", formData.value.home_visit_followup),
                service.buildValueCoded('Agrees to followup', "Home visit")
            ]
        },
        validation: () => {
            validationMessages.value['home_visit_followup'] = ''
            if (!formData.value.home_visit_followup) {
                validationMessages.value['home_visit_followup'] = 'Home visit followup is required'
            }
        }
    },
    has_linkage: {
        required: () => true,
        label: 'Has linkage number',
        options: () => ['Yes', 'No'],
        validation: () => {
            validationMessages.value['has_linkage'] = ''
            if (!formData.value.has_linkage) {
                validationMessages.value['has_linkage'] = 'Has linkage is required'
            }
        }
    },
    linkage_number: {
        required: () => formData.value.has_linkage === 'Yes',
        label: 'Linkage number',
        onFormUpdate: (field: string, value: any) => {
            if (field === 'has_linkage' && value === 'Yes') {
                formData.value.linkage_number = ''
                validationMessages.value['linkage_number'] = ''
            }
        },
        buildObs: () => {
            return service.buildValueText('HTC Serial number', formData.value.linkage_number)
        },
        validation: () => {
            validationMessages.value['linkage_number'] = ''
            const required = formModel.linkage_number.required()
            if (required && !formData.value.linkage_number) {
                validationMessages.value['linkage_number'] = 'Linkage number is required'
                return
            }
            if (required) {
                try {

                    if (!validateScanFormLinkageCode(formData.value.linkage_number)) {
                        validationMessages.value['linkage_number'] = 'Invalid linkage number'
                    }
                } catch (e) {
                    validationMessages.value['linkage_number'] = 'Invalid linkage number. please follow format "1234-002-01-A" '
                }
            }
        }
    },
    received_arvs: {
        required: () => true,
        label: 'Received ARVs',
        options: () => ['Yes', 'No'],
        buildObs: () => {
            return service.buildValueCoded('Ever received ART', formData.value.received_arvs)
        },
        validation: () => {
            validationMessages.value['received_arvs'] = ''
            if (!formData.value.received_arvs) {
                validationMessages.value['received_arvs'] = 'Received ARVs is required'
            }
        }
    },
    date_last_taken_arvs: {
        required: () => formData.value.received_arvs === 'Yes',
        label: 'Date last taken ARVs',
        onFormUpdate: (field: string, value: any) => {
            if (field === 'received_arvs' && value === 'No') {
                formData.value.date_last_taken_arvs = ''
                validationMessages.value['date_last_taken_arvs'] = ''
            }
        },
        buildObs: () => {
            return service.buildValueDate('Date ART last taken', formData.value.date_last_taken_arvs)
        },
        validation: () => {
            validationMessages.value['date_last_taken_arvs'] = ''
            const required = formModel.date_last_taken_arvs.required()
            if (required && !formData.value.date_last_taken_arvs) {
                validationMessages.value['date_last_taken_arvs'] = 'Date last taken ARVs is required'
            }
        }
    },
    taken_last_two_months: {
        required: () => formData.value.received_arvs === 'Unknown',
        label: 'Taken last two months',
        onFormUpdate: (field: string, value: any) => {
            if (field === 'received_arvs' && value == 'No') {
                formData.value.taken_last_two_months = ''
                validationMessages.value['taken_last_two_months'] = ''
            }
        },
        options: () => ['Yes', 'No', 'Unknown'],
        buildObs: () => {
            return service.buildValueCoded('Has the patient taken ART in the last two months', formData.value.taken_last_two_months)
        },
        validation: () => {
            validationMessages.value['taken_last_two_months'] = ''
            const required = formModel.taken_last_two_months.required()
            if (required && !formData.value.taken_last_two_months) {
                validationMessages.value['taken_last_two_months'] = 'Taken last two months is required'
            }
        }
    },
    taken_last_two_weeks: {
        required: () => formData.value.taken_last_two_months === 'Yes',
        label: 'Taken last two weeks',
        onFormUpdate: (field: string, value: any) => {
            if (field === 'taken_last_two_months' && value === 'No') {
                formData.value.taken_last_two_weeks = ''
                validationMessages.value['taken_last_two_weeks'] = ''
            }
        },
        options: () => ['Yes', 'No', 'Unknown'],
        buildObs: () => {
            return service.buildValueCoded('Has the patient taken ART in the last two weeks', formData.value.taken_last_two_weeks)
        },
        validation: () => {
            validationMessages.value['taken_last_two_weeks'] = ''
            const required = formModel.taken_last_two_weeks.required()
            if (required && !formData.value.taken_last_two_weeks) {
                validationMessages.value['taken_last_two_weeks'] = 'Taken last two weeks is required'
            }
        }
    },
    ever_registered_at_art_clinic: {
        required: () => formData.value.received_arvs === 'Yes',
        label: 'Ever registered at ART clinic',
        onFormUpdate: (field: string, value: any) => {
            if (field === 'received_arvs' && value === 'No') {
                formData.value.ever_registered_at_art_clinic = ''
                validationMessages.value['ever_registered_at_art_clinic'] = ''
            }
        },
        options: () => ['Yes', 'No'],
        buildObs: () => {
            return service.buildValueCoded('Ever registered at ART clinic', formData.value.ever_registered_at_art_clinic)
        },
        validation: () => {
            validationMessages.value['ever_registered_at_art_clinic'] = ''
            const required = formModel.ever_registered_at_art_clinic.required()
            if (required && !formData.value.ever_registered_at_art_clinic) {
                validationMessages.value['ever_registered_at_art_clinic'] = 'Ever registered at ART clinic is required'
            }
        }
    },
    location_of_art_initialization: {
        required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
        label: 'Location of ART initialization',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'ever_registered_at_art_clinic' && value === 'No')) {
                formData.value.location_of_art_initialization = ''
                validationMessages.value['location_of_art_initialization'] = ''
            }
        },
        buildObs: () => {
            return service.buildValueText('Location of ART initialization', formData.value.location_of_art_initialization?.name ?? formData.value.location_of_art_initialization)
        },
        validation: () => {
            validationMessages.value['location_of_art_initialization'] = ''
            if (formData.value.ever_registered_at_art_clinic === 'Yes' && !formData.value.location_of_art_initialization) {
                validationMessages.value['location_of_art_initialization'] = 'Location of ART initialization is required'
            }
        },
        searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res)
    },
    art_start_date: {
        required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
        label: 'ART start date',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'ever_registered_at_art_clinic' && value === 'No')) {
                formData.value.art_start_date = ''
                validationMessages.value['art_start_date'] = ''
            }
        },
        buildObs: () => {
            return buildDateObs('Date ART started', formData.value.art_start_date, false)
        },
        validation: () => {
            validationMessages.value['art_start_date'] = ''
            const required = formModel.art_start_date.required()
            if (required && !formData.value.art_start_date) {
                validationMessages.value['art_start_date'] = 'ART start date is required'
                return
            }
            if (required && new Date(formData.value.art_start_date) > new Date()) {
                validationMessages.value['art_start_date'] = 'ART start date cannot be in the future'
            }
            if (required && new Date(formData.value.art_start_date) < new Date(patient.getBirthdate())) {
                validationMessages.value['art_start_date'] = 'ART start date cannot be before birthdate'
            }
        }
    },
    art_number_at_previous_location: {
        required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
        label: 'ART number at previous location',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'ever_registered_at_art_clinic' && value === 'No')) {
                formData.value.art_number_at_previous_location = ''
                validationMessages.value['art_number_at_previous_location'] = ''
            }
        },
        buildObs: () => {
            return service.buildValueText('ART number at previous location', formData.value.art_number_at_previous_location?.name ?? formData.value.art_number_at_previous_location)
        },
        validation: () => {
            validationMessages.value['art_number_at_previous_location'] = ''
            const required = formModel.art_number_at_previous_location.required()
            if (required && !formData.value.art_number_at_previous_location) {
                validationMessages.value['art_number_at_previous_location'] = 'ART number at previous location is required'
            }
        }
    },
    has_transfer_letter: {
        required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'ever_registered_at_art_clinic' && value === 'No')) {
                formData.value.has_transfer_letter = ''
                validationMessages.value['has_transfer_letter'] = ''
            }
        },
        label: 'Has stage information?',
        options: () => ['Yes', 'No'],
        buildObs: () => {
            return service.buildValueCoded('Has transfer letter', formData.value.has_transfer_letter)
        },
        validation: () => {
            validationMessages.value['has_transfer_letter'] = ''
            const required = formModel.has_transfer_letter.required()
            if (required && !formData.value.has_transfer_letter) {
                validationMessages.value['has_transfer_letter'] = 'Has transfer letter is required'
            }
        }
    },
    initial_height: {
        required: () => formData.value.has_transfer_letter === 'Yes',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'has_transfer_letter' && value === 'No')) {
                formData.value.initial_height = ''
                validationMessages.value['initial_height'] = ''
            }
        },
        label: 'Initial height',
        buildObs: () => {
            return service.buildValueNumber('Height', formData.value.initial_height)
        },
        validation: () => {
            validationMessages.value['initial_height'] = ''
            const required = formModel.initial_height.required()
            if (required && !formData.value.initial_height) {
                validationMessages.value['initial_height'] = 'Initial height is required'
                return
            }
            if (required && !/^\d+$/i.test(`${formData.value.initial_height}`)) {
                validationMessages.value['initial_height'] = 'Initial height must be a number'
            }
            if (required && formData.value.initial_height < 40) {
                validationMessages.value['initial_height'] = 'Initial height cannot be less than 40'
            }
            if (required && formData.value.initial_height > 220) {
                validationMessages.value['initial_height'] = 'Initial height cannot be greater than 220'
            }
        }
    },
    initial_weight: {
        required: () => formData.value.has_transfer_letter === 'Yes',
        label: 'Initial weight',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'has_transfer_letter' && value === 'No')) {
                formData.value.initial_weight = ''
                validationMessages.value['initial_weight'] = ''
            }
        },
        buildObs: () => {
            return service.buildValueNumber('Weight', formData.value.initial_weight)
        },
        validation: () => {
            validationMessages.value['initial_weight'] = ''
            const required = formModel.initial_weight.required()
            if (required && !formData.value.initial_weight) {
                validationMessages.value['initial_weight'] = 'Initial weight is required'
                return
            }
            if (required && !/^\d{1,3}\.\d{1}$/i.test(`${formData.value.initial_weight}`)) {
                validationMessages.value['initial_weight'] = 'Initial weight must be a valid decimal number.. i.e. 40.0'
            }
            if (required && parseInt(formData.value.initial_weight) < 0.5) {
                validationMessages.value['initial_weight'] = 'Initial weight cannot be less than 0.5'
            }
            if (required && parseInt(formData.value.initial_weight) > 250) {
                validationMessages.value['initial_weight'] = 'Initial weight cannot be greater than 250'
            }
        }
    },
    cd4_available: {
        required: () => formData.value.has_transfer_letter === 'Yes',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'has_transfer_letter' && value === 'No')) {
                formData.value.cd4_available = ''
                validationMessages.value['cd4_available'] = ''
            }
        },
        label: 'CD4 available',
        options: () => ['Yes', 'No'],
        validation: () => {
            validationMessages.value['cd4_available'] = ''
            const required = formModel.cd4_available.required()
            if (required && !formData.value.cd4_available) {
                validationMessages.value['cd4_available'] = 'CD4 available is required'
            }
        }
    },
    cd4_percent: {
        required: () => formData.value.cd4_available === 'Yes',
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === 'No') || (field === 'cd4_available' && value === 'No')) {
                formData.value.cd4_percent = ''
                validationMessages.value['cd4_percent'] = ''
            }
        },
        label: 'CD4 percent',
        buildObs: () => {
            return service.buildValueNumber(
                'CD4 percent', parseInt(formData.value.cd4_percent.substring(1)), '%' as any
            )
        },
        validation: () => {
            validationMessages.value['cd4_percent'] = ''
            const required = formModel.cd4_percent.required()
            if (required && !formData.value.cd4_percent) {
                validationMessages.value['cd4_percent'] = 'CD4 percent is required'
                return
            }
            if (required && !/^\d+$/i.test(`${formData.value.cd4_percent}`)) {
                validationMessages.value['cd4_percent'] = 'CD4 percent must be a number'
            }
            if (required && parseInt(formData.value.cd4_percent) < 0) {
                validationMessages.value['cd4_percent'] = 'CD4 percent cannot be less than 0'
            }
            if (required && parseInt(formData.value.cd4_percent) > 100) {
                validationMessages.value['cd4_percent'] = 'CD4 percent cannot be greater than 100'
            }
        }
    },
    confirmatory_hiv_test_type: {
        required: () => true,
        label: 'Confirmatory hiv test type',
        options: () => ([
            { label: 'Rapid antibody test', value: 'HIV rapid test' },
            { label: 'DNA PCR', value: 'HIV DNA polymerase chain reaction' },
            { label: 'Not done', value: 'Not done', disabled: () => formData.value.has_linkage === 'Yes' }
        ]),
        buildObs: () => {
            return service.buildValueCoded('Confirmatory hiv test type', formData.value.confirmatory_hiv_test_type)
        },
        validation: () => {
            validationMessages.value['confirmatory_hiv_test_type'] = ''
            if (!formData.value.confirmatory_hiv_test_type) {
                validationMessages.value['confirmatory_hiv_test_type'] = 'Confirmatory hiv test type is required'
                return
            }
            if (formData.value.confirmatory_hiv_test_type === 'Not done' && formData.value.has_linkage === 'Yes') {
                validationMessages.value['confirmatory_hiv_test_type'] = 'Linkage number detected, please select a confirmatory hiv test type'
            }
        }
    },
    confirmatory_hiv_test_location: {
        required: () => formData.value.confirmatory_hiv_test_type && formData.value.confirmatory_hiv_test_type !== 'Not done',
        onFormUpdate: (field: string, value: any) => {
            if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                formData.value.confirmatory_hiv_test_location = ''
                validationMessages.value['confirmatory_hiv_test_location'] = ''
            }
        },
        label: 'Confirmatory hiv test location',
        buildObs: () => {
            return service.buildValueText('Confirmatory hiv test location', formData.value.confirmatory_hiv_test_location?.name ?? formData.value.confirmatory_hiv_test_location)
        },
        searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res),
        validation: () => {
            validationMessages.value['confirmatory_hiv_test_location'] = ''
            const required = formModel.confirmatory_hiv_test_location.required()
            if (required && !formData.value.confirmatory_hiv_test_location) {
                validationMessages.value['confirmatory_hiv_test_location'] = 'Confirmatory hiv test location is required'
            }
        }
    },
    confirmatory_hiv_test_date: {
        required: () => formData.value.confirmatory_hiv_test_type && formData.value.confirmatory_hiv_test_type !== 'Not done',
        onFormUpdate: (field: string, value: any) => {
            if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                formData.value.confirmatory_hiv_test_date = ''
                validationMessages.value['confirmatory_hiv_test_date'] = ''
            }
        },
        label: 'Confirmatory hiv test date',
        buildObs: () => {
            return buildDateObs('Confirmatory hiv test date', formData.value.confirmatory_hiv_test_date, false)
        },
        validation: () => {
            validationMessages.value['confirmatory_hiv_test_date'] = ''
            const required = formModel.confirmatory_hiv_test_date.required()
            if (required && !formData.value.confirmatory_hiv_test_date) {
                validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date is required'
                return
            }
            if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) > new Date()) {
                validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be in the future'
            }
            if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) < new Date(patient.getBirthdate())) {
                validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be before birthdate'
            }
        }
    }
}

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && formModel[c].required() && typeof formModel[c].buildObs === 'function') {
            const obs = formModel[c].buildObs()
            if (Array.isArray(obs)) {
                return [...a, ...obs]
            }
            return [...a, obs]
        }
        return a
    }, [])
}

function dataHandler(field: string, value: any) {
    runValidation(field)
    Object.keys(formModel).forEach((k) => formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, value))
}



function buildDateObs(conceptName: string, date: string, isEstimate: boolean) {
    let obs = {}
    if (date.match(/unknown/i)) {
        obs = service.buildValueText(conceptName, 'Unknown')
    } else if (isEstimate) {
        obs = service.buildValueDateEstimated(conceptName, date)
    } else {
        obs = service.buildValueDate(conceptName, date)
    }
    return obs
}

function runValidation(field: string) {
    validationMessages.value[field] = ''
    if (typeof formModel[field].required === 'function') {
        const required = formModel[field].required()
        if (required && field in formData.value && !formData.value[field]) {
            validationMessages.value[field] = 'This field is required'
            return
        }
    }
    if (typeof formModel[field].validation === 'function') {
        formModel[field].validation()
    }
}

function validateAll() {
    Object.keys(formModel).forEach((key: string) => runValidation(key))
    return Object.keys(validationMessages.value).every((key) => `${validationMessages.value[key]}`.length <= 0)
}

async function onSubmit() {
    const stagingOk = (() => {
        if (formData.value.has_transfer_letter === 'Yes') {
            return stagingRef.value.validateStagingForm()
        }
        return true
    })()
    const registrationOk = validateAll()
    if (!stagingOk || !registrationOk) {
        toastWarning("Please review form for errors")
        return false
    }
    try {
        await service.createEncounter()
        const obs = await Promise.all(buildObs())
        await service.onSubmit(obs)
        if (formData.value.has_transfer_letter === 'Yes') {
            if (!(await stagingRef.value.onSubmit())) {
                toastDanger("Unable to save Staging information!")
                return false
            }
        }
        toastSuccess("ART Clinic Registration saved successfully")
        return true
    } catch (e) {
        console.error(e)
        toastDanger("Error has occured while saving observations")
        return false
    }
}

defineExpose({
    onSubmit
})
</script>

<style scoped>
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-label span {
    color: #eb445a;
}

ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
}

ion-radio {
    margin-right: 8px;
}

ion-note {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    display: block;
    color: #eb445a !important;
    font-weight: bold;
    font-style: italic;
}
</style>